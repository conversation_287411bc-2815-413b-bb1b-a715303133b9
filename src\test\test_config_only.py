"""
简化的配置测试
只测试配置管理功能，避免复杂的依赖问题
"""

import sys
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.config import config_manager


def test_configuration():
    """测试配置功能"""
    print("=== 测试配置管理 ===")
    
    # 测试配置加载
    print(f"MinerU并发数: {config_manager.concurrency.mineru_max_concurrent}")
    print(f"Native并发数: {config_manager.concurrency.native_max_concurrent}")
    print(f"Pandoc并发数: {config_manager.concurrency.pandoc_max_concurrent}")
    print(f"总并发数: {config_manager.concurrency.max_total_concurrent_tasks}")
    print(f"使用snow_id子目录: {config_manager.directory.use_snow_id_subdirs}")
    print(f"检查已存在文件: {config_manager.processing.check_existing_files}")
    
    assert config_manager.concurrency.mineru_max_concurrent > 0
    assert config_manager.concurrency.native_max_concurrent > 0
    assert config_manager.concurrency.pandoc_max_concurrent > 0
    print("✓ 配置加载正确")


def test_directory_structure():
    """测试目录结构"""
    print("\n=== 测试目录结构 ===")
    
    # 确保配置使用snow_id子目录
    assert config_manager.directory.use_snow_id_subdirs == True
    
    # 测试路径生成
    test_path = config_manager.get_file_path(
        "test_doc", "123456789", "test.pdf", "receive"
    )
    
    expected_path = Path(config_manager.directory.base_receive_dir) / "test_doc" / "123456789" / "test.pdf"
    assert test_path == expected_path
    
    print(f"✓ 目录结构正确: {test_path}")


def test_path_generation():
    """测试路径生成"""
    print("\n=== 测试路径生成 ===")
    
    document_id = "test_doc"
    snow_id = "123456789"
    filename = "test.pdf"
    
    # 测试不同类型的路径生成
    receive_path = config_manager.get_file_path(document_id, snow_id, filename, "receive")
    markdown_path = config_manager.get_file_path(document_id, snow_id, filename, "markdown")
    tree_path = config_manager.get_file_path(document_id, snow_id, filename, "tree")
    
    print(f"✓ 接收路径: {receive_path}")
    print(f"✓ Markdown路径: {markdown_path}")
    print(f"✓ 文档树路径: {tree_path}")
    
    # 验证路径结构（使用系统路径分隔符）
    assert receive_path.name == filename
    assert receive_path.parent.name == snow_id
    assert receive_path.parent.parent.name == document_id
    print("✓ 路径结构验证正确")


def test_file_existence_check():
    """测试文件存在性检查"""
    print("\n=== 测试文件存在性检查 ===")
    
    # 清理测试环境
    cleanup_test_files()
    
    # 创建测试文件
    test_document_id = "test_knowledge_base"
    test_filename = "test.pdf"
    
    # 创建测试文件路径
    test_file_path = config_manager.get_file_path(
        test_document_id, "123456789", test_filename, "receive"
    )
    
    # 确保目录存在
    test_file_path.parent.mkdir(parents=True, exist_ok=True)
    
    # 创建测试文件
    test_file_path.write_text("test content")
    
    # 测试文件存在性检查
    exists, existing_path = config_manager.check_file_exists(test_document_id, test_filename)
    
    print(f"文件是否存在: {exists}")
    print(f"已存在文件路径: {existing_path}")
    
    assert exists == True
    assert existing_path is not None
    print("✓ 文件存在性检查正确")
    
    # 清理
    cleanup_test_files()


def cleanup_test_files():
    """清理测试文件"""
    try:
        cleanup_dirs = [
            config_manager.directory.base_receive_dir,
            config_manager.directory.base_markdown_dir,
            config_manager.directory.base_document_tree_dir
        ]
        
        for dir_name in cleanup_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists():
                shutil.rmtree(dir_path)
        
        print("测试文件清理完成")
    except Exception as e:
        print(f"清理失败: {e}")


def test_config_save_load():
    """测试配置保存和加载"""
    print("\n=== 测试配置保存和加载 ===")
    
    # 修改配置
    original_mineru = config_manager.concurrency.mineru_max_concurrent
    config_manager.concurrency.mineru_max_concurrent = 5
    
    # 保存配置
    config_manager.save_config()
    
    # 重新加载配置
    config_manager._load_config()
    
    # 验证配置是否正确保存和加载
    assert config_manager.concurrency.mineru_max_concurrent == 5
    print("✓ 配置保存和加载正确")
    
    # 恢复原始配置
    config_manager.concurrency.mineru_max_concurrent = original_mineru
    config_manager.save_config()


def main():
    """运行所有测试"""
    print("开始测试配置管理功能...")
    
    try:
        test_configuration()
        test_directory_structure()
        test_path_generation()
        test_file_existence_check()
        test_config_save_load()
        
        print("\n=== 所有配置测试完成 ===")
        print("✓ 所有测试通过")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cleanup_test_files()


if __name__ == "__main__":
    main()
