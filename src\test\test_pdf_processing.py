"""
测试PDF处理功能
测试使用A、B、C PDF文件的处理流程
"""

import sys
import shutil
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.config import config_manager


def cleanup_test_files():
    """清理测试文件"""
    try:
        cleanup_dirs = [
            config_manager.directory.base_receive_dir,
            config_manager.directory.base_markdown_dir,
            config_manager.directory.base_document_tree_dir
        ]
        
        for dir_name in cleanup_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists():
                shutil.rmtree(dir_path)
        
        print("测试文件清理完成")
    except Exception as e:
        print(f"清理失败: {e}")


def test_pdf_files_exist():
    """测试PDF文件是否存在"""
    print("=== 检查测试PDF文件 ===")
    
    test_files = [
        "src/test/A.pdf",
        "src/test/B.pdf", 
        "src/test/C.pdf"
    ]
    
    existing_files = []
    for file_path in test_files:
        test_file = Path(file_path)
        if test_file.exists():
            file_size = test_file.stat().st_size
            print(f"✓ 找到测试文件: {file_path} (大小: {file_size} bytes)")
            existing_files.append(test_file)
        else:
            print(f"✗ 测试文件不存在: {file_path}")
    
    return existing_files


async def test_simple_file_processing():
    """测试简单的文件处理流程"""
    print("\n=== 测试简单文件处理 ===")
    
    # 清理环境
    cleanup_test_files()
    
    # 检查测试文件
    existing_files = test_pdf_files_exist()
    
    if not existing_files:
        print("没有找到测试PDF文件，跳过处理测试")
        return
    
    # 只测试第一个文件
    test_file = existing_files[0]
    document_id = "test_knowledge_base_001"
    
    print(f"\n处理文件: {test_file.name}")
    
    try:
        # 模拟文件上传过程
        with open(test_file, 'rb') as f:
            file_content = f.read()
        
        print(f"✓ 读取文件成功，大小: {len(file_content)} bytes")
        
        # 测试目录结构创建
        from src.utils.DataProcess import SnowflakeIDGenerator
        snowflake = SnowflakeIDGenerator()
        snow_id = str(snowflake.next_id())
        
        # 创建文件路径
        file_path = config_manager.get_file_path(
            document_id, snow_id, test_file.name, "receive"
        )
        
        # 确保目录存在
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入文件
        with open(file_path, 'wb') as f:
            f.write(file_content)
        
        print(f"✓ 文件保存成功: {file_path}")
        
        # 验证目录结构
        assert file_path.name == test_file.name
        assert file_path.parent.name == snow_id
        assert file_path.parent.parent.name == document_id
        print("✓ 目录结构正确")
        
        # 测试文件存在性检查
        exists, existing_path = config_manager.check_file_exists(document_id, test_file.name)
        assert exists == True
        print(f"✓ 文件存在性检查正确: {existing_path}")
        
        # 创建元数据
        metadata = {
            "raw_file_name": test_file.name,
            "file_type": "pdf",
            "snow_id": snow_id,
            "tmp_file_name": test_file.name,
            "timestamp": "1234567890",
            "document_id": document_id,
            "file_path": str(file_path)
        }
        
        print(f"✓ 元数据创建成功: {metadata}")
        
        # 测试markdown路径生成
        markdown_path = config_manager.get_file_path(
            document_id, snow_id, f"{snow_id}_markdown.md", "markdown"
        )
        print(f"✓ Markdown路径: {markdown_path}")
        
        # 测试文档树路径生成
        tree_path = config_manager.get_file_path(
            document_id, snow_id, "document_tree", "tree"
        )
        print(f"✓ 文档树路径: {tree_path}")
        
    except Exception as e:
        print(f"✗ 处理失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        cleanup_test_files()


def test_multiple_documents():
    """测试多个文档的处理"""
    print("\n=== 测试多文档处理 ===")
    
    # 清理环境
    cleanup_test_files()
    
    # 检查测试文件
    existing_files = test_pdf_files_exist()
    
    if len(existing_files) < 2:
        print("测试文件不足，跳过多文档测试")
        return
    
    try:
        from src.utils.DataProcess import SnowflakeIDGenerator
        snowflake = SnowflakeIDGenerator()
        
        # 处理多个文件到同一个知识库
        document_id = "multi_doc_knowledge_base"
        
        for i, test_file in enumerate(existing_files[:3]):  # 最多处理3个文件
            snow_id = str(snowflake.next_id())
            
            # 读取文件
            with open(test_file, 'rb') as f:
                file_content = f.read()
            
            # 创建文件路径
            file_path = config_manager.get_file_path(
                document_id, snow_id, test_file.name, "receive"
            )
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件
            with open(file_path, 'wb') as f:
                f.write(file_content)
            
            print(f"✓ 文件 {i+1} 保存成功: {test_file.name} -> {file_path}")
        
        # 验证知识库结构
        kb_dir = Path(config_manager.directory.base_receive_dir) / document_id
        subdirs = [d for d in kb_dir.iterdir() if d.is_dir()]
        
        print(f"✓ 知识库 {document_id} 包含 {len(subdirs)} 个文档子目录")
        
        for subdir in subdirs:
            files = list(subdir.glob("*.pdf"))
            if files:
                print(f"  - {subdir.name}: {files[0].name}")
        
    except Exception as e:
        print(f"✗ 多文档处理失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        cleanup_test_files()


def test_duplicate_file_handling():
    """测试重复文件处理"""
    print("\n=== 测试重复文件处理 ===")
    
    # 清理环境
    cleanup_test_files()
    
    # 检查测试文件
    existing_files = test_pdf_files_exist()
    
    if not existing_files:
        print("没有找到测试PDF文件，跳过重复文件测试")
        return
    
    test_file = existing_files[0]
    document_id = "duplicate_test_kb"
    
    try:
        from src.utils.DataProcess import SnowflakeIDGenerator
        snowflake = SnowflakeIDGenerator()
        
        # 第一次上传
        snow_id1 = str(snowflake.next_id())
        
        with open(test_file, 'rb') as f:
            file_content = f.read()
        
        file_path1 = config_manager.get_file_path(
            document_id, snow_id1, test_file.name, "receive"
        )
        file_path1.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path1, 'wb') as f:
            f.write(file_content)
        
        print(f"✓ 第一次上传成功: {file_path1}")
        
        # 检查文件是否存在
        exists, existing_path = config_manager.check_file_exists(document_id, test_file.name)
        assert exists == True
        print(f"✓ 文件存在性检查正确: {existing_path}")
        
        # 模拟第二次上传同一个文件
        print("模拟第二次上传同一个文件...")
        exists2, existing_path2 = config_manager.check_file_exists(document_id, test_file.name)
        
        if exists2:
            print(f"✓ 检测到重复文件，应该跳过: {existing_path2}")
        else:
            print("✗ 未能检测到重复文件")
        
    except Exception as e:
        print(f"✗ 重复文件处理测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        cleanup_test_files()


async def main():
    """运行所有测试"""
    print("开始测试PDF处理功能...")
    
    try:
        # 检查测试文件
        test_pdf_files_exist()
        
        # 运行测试
        await test_simple_file_processing()
        test_multiple_documents()
        test_duplicate_file_handling()
        
        print("\n=== 所有PDF处理测试完成 ===")
        print("✓ 测试完成")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
