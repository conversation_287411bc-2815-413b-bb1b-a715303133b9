"""
最终验证测试
验证所有改进功能的综合效果
"""

import sys
import shutil
import time
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.config import config_manager


class SimpleSnowflakeIDGenerator:
    """简化的雪花ID生成器"""
    
    def __init__(self):
        self.sequence = 0
        self.last_timestamp = 0
    
    def _current_millis(self):
        return int(time.time() * 1000)
    
    def next_id(self):
        timestamp = self._current_millis()
        if timestamp == self.last_timestamp:
            self.sequence += 1
        else:
            self.sequence = 0
        self.last_timestamp = timestamp
        return timestamp * 1000 + self.sequence


def cleanup_test_files():
    """清理测试文件"""
    try:
        cleanup_dirs = [
            config_manager.directory.base_receive_dir,
            config_manager.directory.base_markdown_dir,
            config_manager.directory.base_document_tree_dir
        ]
        
        for dir_name in cleanup_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists():
                shutil.rmtree(dir_path)
        
        # 清理配置文件
        config_file = Path("config.json")
        if config_file.exists():
            config_file.unlink()
        
        print("测试环境清理完成")
    except Exception as e:
        print(f"清理失败: {e}")


def test_complete_workflow():
    """测试完整的工作流程"""
    print("=== 完整工作流程测试 ===")
    
    # 清理环境
    cleanup_test_files()
    
    # 检查测试文件
    test_files = [
        "src/test/A.pdf",
        "src/test/B.pdf", 
        "src/test/C.pdf"
    ]
    
    existing_files = []
    for file_path in test_files:
        test_file = Path(file_path)
        if test_file.exists():
            existing_files.append(test_file)
    
    if not existing_files:
        print("没有找到测试PDF文件")
        return False
    
    print(f"找到 {len(existing_files)} 个测试文件")
    
    try:
        snowflake = SimpleSnowflakeIDGenerator()
        
        # 场景1：创建多个知识库，每个知识库包含多个文档
        knowledge_bases = {
            "技术文档库": existing_files[:2] if len(existing_files) >= 2 else existing_files[:1],
            "产品手册库": existing_files[1:] if len(existing_files) >= 2 else existing_files[:1],
        }
        
        all_metadata = {}
        
        for kb_name, files in knowledge_bases.items():
            print(f"\n处理知识库: {kb_name}")
            kb_metadata = []
            
            for file in files:
                snow_id = str(snowflake.next_id())
                
                # 读取文件
                with open(file, 'rb') as f:
                    file_content = f.read()
                
                # 创建文件路径
                file_path = config_manager.get_file_path(
                    kb_name, snow_id, file.name, "receive"
                )
                
                # 确保目录存在
                file_path.parent.mkdir(parents=True, exist_ok=True)
                
                # 写入文件
                with open(file_path, 'wb') as f:
                    f.write(file_content)
                
                # 创建元数据
                metadata = {
                    "raw_file_name": file.name,
                    "file_type": "pdf",
                    "snow_id": snow_id,
                    "tmp_file_name": file.name,
                    "timestamp": str(int(time.time())),
                    "document_id": kb_name,
                    "file_path": str(file_path)
                }
                
                kb_metadata.append(metadata)
                print(f"  ✓ 添加文档: {file.name} -> {snow_id}")
            
            all_metadata[kb_name] = kb_metadata
        
        # 验证目录结构
        print("\n验证目录结构:")
        base_dir = Path(config_manager.directory.base_receive_dir)
        
        for kb_name in knowledge_bases.keys():
            kb_dir = base_dir / kb_name
            if kb_dir.exists():
                subdirs = [d for d in kb_dir.iterdir() if d.is_dir()]
                print(f"  {kb_name}: {len(subdirs)} 个文档")
                
                for subdir in subdirs:
                    files = list(subdir.glob("*.pdf"))
                    if files:
                        print(f"    - {subdir.name}: {files[0].name}")
        
        # 场景2：测试重复文件检测
        print("\n测试重复文件检测:")
        first_kb = list(knowledge_bases.keys())[0]
        first_file = existing_files[0]
        
        exists, existing_path = config_manager.check_file_exists(first_kb, first_file.name)
        if exists:
            print(f"  ✓ 检测到已存在文件: {first_file.name}")
            print(f"    路径: {existing_path}")
        else:
            print(f"  ✗ 未能检测到已存在文件: {first_file.name}")
            return False
        
        # 场景3：测试跨知识库的同名文件
        print("\n测试跨知识库同名文件:")
        kb_names = list(knowledge_bases.keys())
        if len(kb_names) >= 2:
            for kb_name in kb_names:
                exists, path = config_manager.check_file_exists(kb_name, first_file.name)
                print(f"  {kb_name}: {'存在' if exists else '不存在'}")
        
        # 场景4：生成处理路径
        print("\n生成处理路径:")
        for kb_name, metadata_list in all_metadata.items():
            for metadata in metadata_list:
                # Markdown路径
                markdown_path = config_manager.get_file_path(
                    metadata["document_id"], 
                    metadata["snow_id"], 
                    f"{metadata['snow_id']}_markdown.md", 
                    "markdown"
                )
                
                # 文档树路径
                tree_path = config_manager.get_file_path(
                    metadata["document_id"], 
                    metadata["snow_id"], 
                    "document_tree", 
                    "tree"
                )
                
                print(f"  {metadata['raw_file_name']}:")
                print(f"    Markdown: {markdown_path}")
                print(f"    文档树: {tree_path}")
        
        print("\n✓ 完整工作流程测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 完整工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        cleanup_test_files()


def test_configuration_management():
    """测试配置管理"""
    print("\n=== 配置管理测试 ===")
    
    try:
        # 保存当前配置
        original_config = {
            "mineru": config_manager.concurrency.mineru_max_concurrent,
            "native": config_manager.concurrency.native_max_concurrent,
            "pandoc": config_manager.concurrency.pandoc_max_concurrent,
        }
        
        # 修改配置
        config_manager.concurrency.mineru_max_concurrent = 3
        config_manager.concurrency.native_max_concurrent = 6
        config_manager.concurrency.pandoc_max_concurrent = 5
        
        # 保存配置
        config_manager.save_config()
        print("✓ 配置保存成功")
        
        # 验证配置文件
        config_file = Path("config.json")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                saved_config = json.load(f)
            
            assert saved_config["concurrency"]["mineru_max_concurrent"] == 3
            assert saved_config["concurrency"]["native_max_concurrent"] == 6
            assert saved_config["concurrency"]["pandoc_max_concurrent"] == 5
            print("✓ 配置文件内容正确")
        
        # 重新加载配置
        config_manager._load_config()
        
        assert config_manager.concurrency.mineru_max_concurrent == 3
        assert config_manager.concurrency.native_max_concurrent == 6
        assert config_manager.concurrency.pandoc_max_concurrent == 5
        print("✓ 配置重新加载正确")
        
        # 恢复原始配置
        config_manager.concurrency.mineru_max_concurrent = original_config["mineru"]
        config_manager.concurrency.native_max_concurrent = original_config["native"]
        config_manager.concurrency.pandoc_max_concurrent = original_config["pandoc"]
        config_manager.save_config()
        
        return True
        
    except Exception as e:
        print(f"✗ 配置管理测试失败: {e}")
        return False


def test_edge_cases():
    """测试边界情况"""
    print("\n=== 边界情况测试 ===")
    
    try:
        # 测试空知识库
        empty_kb = "空知识库"
        exists, path = config_manager.check_file_exists(empty_kb, "不存在的文件.pdf")
        assert exists == False
        print("✓ 空知识库检测正确")
        
        # 测试特殊字符文件名
        special_chars = ["文档 with spaces.pdf", "文档-with-dashes.pdf", "文档_with_underscores.pdf"]
        snowflake = SimpleSnowflakeIDGenerator()
        
        for filename in special_chars:
            snow_id = str(snowflake.next_id())
            file_path = config_manager.get_file_path("特殊字符测试", snow_id, filename, "receive")
            
            # 验证路径生成正确
            assert file_path.name == filename
            print(f"✓ 特殊字符文件名处理正确: {filename}")
        
        # 测试长文件名
        long_filename = "这是一个非常长的文件名" * 10 + ".pdf"
        snow_id = str(snowflake.next_id())
        file_path = config_manager.get_file_path("长文件名测试", snow_id, long_filename, "receive")
        assert file_path.name == long_filename
        print("✓ 长文件名处理正确")
        
        return True
        
    except Exception as e:
        print(f"✗ 边界情况测试失败: {e}")
        return False


def main():
    """运行最终验证测试"""
    print("🚀 开始最终验证测试...")
    print("=" * 50)
    
    test_results = []
    
    try:
        # 运行各项测试
        test_results.append(("配置管理", test_configuration_management()))
        test_results.append(("边界情况", test_edge_cases()))
        test_results.append(("完整工作流程", test_complete_workflow()))
        
        print("\n" + "=" * 50)
        print("📊 测试结果汇总")
        print("=" * 50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:15}: {status}")
            if result:
                passed += 1
        
        print(f"\n📈 总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            print("\n🎉 所有测试都通过了！")
            print("\n✨ 系统改进验证成功：")
            print("  ✅ 正确的目录结构：document_id/snow_id/文档")
            print("  ✅ 精确的文件存在性检查逻辑")
            print("  ✅ 支持多文档知识库管理")
            print("  ✅ 重复文件检测和跳过机制")
            print("  ✅ 可配置的精细化并发控制")
            print("  ✅ 完整的配置管理功能")
            print("  ✅ 边界情况处理")
            print("\n🚀 系统已准备好投入使用！")
        else:
            print(f"\n⚠️  {total - passed} 个测试失败，需要进一步检查")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        cleanup_test_files()


if __name__ == "__main__":
    main()
