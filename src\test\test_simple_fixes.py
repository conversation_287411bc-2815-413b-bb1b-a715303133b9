"""
简化的修复验证测试
避免复杂依赖，直接测试核心修复逻辑
"""

import sys
import shutil
import time
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.config import config_manager


class SimpleSnowflakeIDGenerator:
    """简化的雪花ID生成器"""
    
    def __init__(self):
        self.sequence = 0
        self.last_timestamp = 0
    
    def _current_millis(self):
        return int(time.time() * 1000)
    
    def next_id(self):
        timestamp = self._current_millis()
        if timestamp == self.last_timestamp:
            self.sequence += 1
        else:
            self.sequence = 0
        self.last_timestamp = timestamp
        return timestamp * 1000 + self.sequence


def cleanup_test_files():
    """清理测试文件"""
    try:
        cleanup_dirs = [
            config_manager.directory.base_receive_dir,
            config_manager.directory.base_markdown_dir,
            config_manager.directory.base_document_tree_dir
        ]
        
        for dir_name in cleanup_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists():
                shutil.rmtree(dir_path)
        
        print("测试文件清理完成")
    except Exception as e:
        print(f"清理失败: {e}")


def test_markdown_path_generation():
    """测试Markdown文件路径生成修复"""
    print("=== 测试Markdown文件路径生成修复 ===")
    
    try:
        snowflake = SimpleSnowflakeIDGenerator()
        document_id = "test_markdown_path"
        snow_id = str(snowflake.next_id())
        
        # 测试新的路径生成逻辑
        markdown_path = config_manager.get_file_path(
            document_id, snow_id, f"{snow_id}_markdown.md", "markdown"
        )
        
        print(f"生成的Markdown路径: {markdown_path}")
        
        # 验证路径结构
        expected_parts = [
            config_manager.directory.base_markdown_dir,
            document_id,
            snow_id,
            f"{snow_id}_markdown.md"
        ]
        
        path_parts = markdown_path.parts
        
        # 检查路径是否包含正确的组件
        assert document_id in str(markdown_path), f"路径中缺少document_id: {document_id}"
        assert snow_id in str(markdown_path), f"路径中缺少snow_id: {snow_id}"
        assert markdown_path.name == f"{snow_id}_markdown.md", f"文件名不正确: {markdown_path.name}"
        
        print("✓ Markdown路径生成正确")
        
        # 测试文档树路径生成
        tree_path = config_manager.get_file_path(
            document_id, snow_id, "document_tree", "tree"
        )
        
        print(f"生成的文档树路径: {tree_path}")
        
        assert document_id in str(tree_path), f"文档树路径中缺少document_id: {document_id}"
        assert snow_id in str(tree_path), f"文档树路径中缺少snow_id: {snow_id}"
        
        print("✓ 文档树路径生成正确")
        
        return True
        
    except Exception as e:
        print(f"✗ Markdown路径生成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_duplicate_check_logic():
    """测试文档重复检查逻辑修复"""
    print("\n=== 测试文档重复检查逻辑修复 ===")
    
    cleanup_test_files()
    
    try:
        snowflake = SimpleSnowflakeIDGenerator()
        document_id = "test_duplicate_logic"
        
        # 测试文件列表
        test_files = ["document1.pdf", "document2.pdf", "document3.pdf"]
        
        print("第一步：验证文件不存在")
        for filename in test_files:
            exists, existing_path = config_manager.check_file_exists(document_id, filename)
            if exists:
                print(f"✗ 文件误报存在: {filename}")
                return False
            else:
                print(f"✓ 文件正确不存在: {filename}")
        
        print("\n第二步：创建文件")
        created_files = []
        
        for filename in test_files:
            snow_id = str(snowflake.next_id())
            
            # 创建文件路径
            file_path = config_manager.get_file_path(
                document_id, snow_id, filename, "receive"
            )
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入测试内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"测试内容 - {filename}")
            
            created_files.append((filename, snow_id, file_path))
            print(f"✓ 创建文件: {filename} -> {file_path}")
        
        print("\n第三步：验证文件存在")
        for filename, snow_id, file_path in created_files:
            exists, existing_path = config_manager.check_file_exists(document_id, filename)
            if not exists:
                print(f"✗ 文件检测失败: {filename}")
                return False
            else:
                print(f"✓ 文件正确检测: {filename} -> {existing_path}")
                # 验证返回的路径是否正确
                assert str(existing_path) == str(file_path), f"路径不匹配: {existing_path} != {file_path}"
        
        print("\n第四步：测试新文件")
        new_files = ["new_document1.pdf", "new_document2.pdf"]
        
        for filename in new_files:
            exists, existing_path = config_manager.check_file_exists(document_id, filename)
            if exists:
                print(f"✗ 新文件误报存在: {filename}")
                return False
            else:
                print(f"✓ 新文件正确不存在: {filename}")
        
        print("\n第五步：测试跨知识库隔离")
        other_document_id = "other_knowledge_base"
        
        for filename in test_files:
            exists, existing_path = config_manager.check_file_exists(other_document_id, filename)
            if exists:
                print(f"✗ 跨知识库误报存在: {filename}")
                return False
            else:
                print(f"✓ 跨知识库正确隔离: {filename}")
        
        print("\n✓ 文档重复检查逻辑修复验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 文档重复检查逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        cleanup_test_files()


def test_directory_structure():
    """测试目录结构修复"""
    print("\n=== 测试目录结构修复 ===")
    
    cleanup_test_files()
    
    try:
        snowflake = SimpleSnowflakeIDGenerator()
        
        # 测试多个知识库和文档
        test_cases = [
            ("知识库A", "文档1.pdf"),
            ("知识库A", "文档2.pdf"),
            ("知识库B", "文档1.pdf"),
            ("知识库B", "文档3.pdf"),
        ]
        
        created_paths = []
        
        for document_id, filename in test_cases:
            snow_id = str(snowflake.next_id())
            
            # 创建各种类型的文件路径
            receive_path = config_manager.get_file_path(document_id, snow_id, filename, "receive")
            markdown_path = config_manager.get_file_path(document_id, snow_id, f"{snow_id}_markdown.md", "markdown")
            tree_path = config_manager.get_file_path(document_id, snow_id, "document_tree", "tree")
            
            # 创建目录和文件
            for path in [receive_path, markdown_path, tree_path]:
                path.parent.mkdir(parents=True, exist_ok=True)
                with open(path, 'w', encoding='utf-8') as f:
                    f.write(f"测试内容 - {path.name}")
            
            created_paths.append((document_id, snow_id, filename, receive_path, markdown_path, tree_path))
            print(f"✓ 创建文档: {document_id}/{filename} -> {snow_id}")
        
        print("\n验证目录结构:")
        
        # 验证根目录结构
        base_dirs = [
            Path(config_manager.directory.base_receive_dir),
            Path(config_manager.directory.base_markdown_dir),
            Path(config_manager.directory.base_document_tree_dir)
        ]
        
        for base_dir in base_dirs:
            if base_dir.exists():
                kb_dirs = [d for d in base_dir.iterdir() if d.is_dir()]
                print(f"  {base_dir.name}: {len(kb_dirs)} 个知识库")
                
                for kb_dir in kb_dirs:
                    doc_dirs = [d for d in kb_dir.iterdir() if d.is_dir()]
                    print(f"    - {kb_dir.name}: {len(doc_dirs)} 个文档")
        
        # 验证文件存在性检查
        print("\n验证文件存在性检查:")
        for document_id, snow_id, filename, receive_path, markdown_path, tree_path in created_paths:
            exists, existing_path = config_manager.check_file_exists(document_id, filename)
            if exists:
                print(f"✓ {document_id}/{filename}: 存在")
            else:
                print(f"✗ {document_id}/{filename}: 不存在")
                return False
        
        print("\n✓ 目录结构修复验证通过")
        return True
        
    except Exception as e:
        print(f"✗ 目录结构测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        cleanup_test_files()


def main():
    """运行所有简化修复验证测试"""
    print("🔧 开始简化修复验证...")
    print("=" * 50)
    
    test_results = []
    
    try:
        # 运行各项测试
        test_results.append(("Markdown路径生成", test_markdown_path_generation()))
        test_results.append(("文档重复检查逻辑", test_duplicate_check_logic()))
        test_results.append(("目录结构", test_directory_structure()))
        
        print("\n" + "=" * 50)
        print("📊 简化修复验证结果")
        print("=" * 50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:20}: {status}")
            if result:
                passed += 1
        
        print(f"\n📈 总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            print("\n🎉 所有简化修复验证都通过了！")
            print("\n✨ 修复验证成功：")
            print("  ✅ Markdown文件路径生成逻辑正确")
            print("  ✅ 文档重复检查逻辑正确")
            print("  ✅ 目录结构层级正确")
            print("  ✅ 跨知识库隔离正确")
            print("\n🚀 核心修复完成，可以正常使用！")
        else:
            print(f"\n⚠️  {total - passed} 个测试失败，需要进一步检查")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
