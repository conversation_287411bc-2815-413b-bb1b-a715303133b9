"""
配置管理模块
负责管理系统的各种配置参数，包括并发控制、目录结构等
"""

import os
from dataclasses import dataclass
from typing import Dict, Any, Optional
from pathlib import Path
import json
import logging

logger = logging.getLogger(__name__)


@dataclass
class ConcurrencyConfig:
    """并发控制配置"""
    # PDF转换相关并发控制
    mineru_max_concurrent: int = 2  # MinerU最大并发数
    native_max_concurrent: int = 4  # 原生方法最大并发数
    pandoc_max_concurrent: int = 4  # Pandoc最大并发数
    
    # 全局并发控制
    max_total_concurrent_tasks: int = 10  # 全局最大并发任务数
    
    # 重试配置
    mineru_max_retries: int = 2  # MinerU最大重试次数
    pandoc_max_retries: int = 1  # Pandoc最大重试次数


@dataclass
class DirectoryConfig:
    """目录结构配置"""
    base_receive_dir: str = "tmp_file_receive"  # 文件接收目录
    base_markdown_dir: str = "tmp_file_markdown"  # Markdown输出目录
    base_document_tree_dir: str = "tmp_document_tree"  # 文档树目录
    
    # 目录结构：document_id/snow_id/文档
    use_snow_id_subdirs: bool = True  # 是否使用snow_id作为子目录


@dataclass
class ProcessingConfig:
    """处理配置"""
    # 文件存在性检查
    check_existing_files: bool = True  # 是否检查文件是否已存在
    skip_existing_files: bool = True   # 是否跳过已存在的文件
    
    # 文件类型支持
    supported_pdf_extensions: list = None
    supported_html_extensions: list = None
    supported_markdown_extensions: list = None
    
    def __post_init__(self):
        if self.supported_pdf_extensions is None:
            self.supported_pdf_extensions = ['.pdf']
        if self.supported_html_extensions is None:
            self.supported_html_extensions = ['.html', '.htm']
        if self.supported_markdown_extensions is None:
            self.supported_markdown_extensions = ['.md', '.markdown']


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: Optional[str] = None):
        """
        初始化配置管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        self.config_file = config_file or "config.json"
        self.concurrency = ConcurrencyConfig()
        self.directory = DirectoryConfig()
        self.processing = ProcessingConfig()
        
        # 加载配置
        self._load_config()
        
        # 确保目录存在
        self._ensure_directories()
    
    def _load_config(self):
        """加载配置文件"""
        config_path = Path(self.config_file)
        
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                
                # 更新并发配置
                if 'concurrency' in config_data:
                    concurrency_data = config_data['concurrency']
                    for key, value in concurrency_data.items():
                        if hasattr(self.concurrency, key):
                            setattr(self.concurrency, key, value)
                
                # 更新目录配置
                if 'directory' in config_data:
                    directory_data = config_data['directory']
                    for key, value in directory_data.items():
                        if hasattr(self.directory, key):
                            setattr(self.directory, key, value)
                
                # 更新处理配置
                if 'processing' in config_data:
                    processing_data = config_data['processing']
                    for key, value in processing_data.items():
                        if hasattr(self.processing, key):
                            setattr(self.processing, key, value)
                
                logger.info(f"配置文件加载成功: {config_path}")
                
            except Exception as e:
                logger.warning(f"配置文件加载失败，使用默认配置: {e}")
        else:
            logger.info("配置文件不存在，使用默认配置")
            # 创建默认配置文件
            self.save_config()
    
    def save_config(self):
        """保存配置到文件"""
        config_data = {
            'concurrency': {
                'mineru_max_concurrent': self.concurrency.mineru_max_concurrent,
                'native_max_concurrent': self.concurrency.native_max_concurrent,
                'pandoc_max_concurrent': self.concurrency.pandoc_max_concurrent,
                'max_total_concurrent_tasks': self.concurrency.max_total_concurrent_tasks,
                'mineru_max_retries': self.concurrency.mineru_max_retries,
                'pandoc_max_retries': self.concurrency.pandoc_max_retries,
            },
            'directory': {
                'base_receive_dir': self.directory.base_receive_dir,
                'base_markdown_dir': self.directory.base_markdown_dir,
                'base_document_tree_dir': self.directory.base_document_tree_dir,
                'use_snow_id_subdirs': self.directory.use_snow_id_subdirs,
            },
            'processing': {
                'check_existing_files': self.processing.check_existing_files,
                'skip_existing_files': self.processing.skip_existing_files,
                'supported_pdf_extensions': self.processing.supported_pdf_extensions,
                'supported_html_extensions': self.processing.supported_html_extensions,
                'supported_markdown_extensions': self.processing.supported_markdown_extensions,
            }
        }
        
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            logger.info(f"配置文件保存成功: {self.config_file}")
        except Exception as e:
            logger.error(f"配置文件保存失败: {e}")
    
    def _ensure_directories(self):
        """确保必要的目录存在"""
        directories = [
            self.directory.base_receive_dir,
            self.directory.base_markdown_dir,
            self.directory.base_document_tree_dir
        ]
        
        for dir_path in directories:
            Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    def get_file_path(self, document_id: str, snow_id: str, filename: str, 
                     dir_type: str = "receive") -> Path:
        """
        获取文件路径
        
        Args:
            document_id: 文档ID（知识库ID）
            snow_id: 雪花ID
            filename: 文件名
            dir_type: 目录类型 ("receive", "markdown", "tree")
        
        Returns:
            完整的文件路径
        """
        if dir_type == "receive":
            base_dir = Path(self.directory.base_receive_dir)
        elif dir_type == "markdown":
            base_dir = Path(self.directory.base_markdown_dir)
        elif dir_type == "tree":
            base_dir = Path(self.directory.base_document_tree_dir)
        else:
            raise ValueError(f"不支持的目录类型: {dir_type}")
        
        if self.directory.use_snow_id_subdirs:
            # 使用层级结构：document_id/snow_id/filename
            return base_dir / document_id / snow_id / filename
        else:
            # 使用扁平结构：document_id/filename
            return base_dir / document_id / filename
    
    def check_file_exists(self, document_id: str, original_filename: str) -> tuple[bool, Optional[str]]:
        """
        检查文件是否已存在于知识库中

        Args:
            document_id: 文档ID（知识库ID）
            original_filename: 原始文件名

        Returns:
            (是否存在, 已存在文件的路径)
        """
        if not self.processing.check_existing_files:
            return False, None

        # 检查接收目录中是否已有同名文件
        receive_dir = Path(self.directory.base_receive_dir) / document_id

        if not receive_dir.exists():
            return False, None

        # 遍历所有子目录查找同名文件
        for item in receive_dir.rglob("*"):
            if item.is_file():
                # 检查是否是同一个原始文件名
                if item.name == original_filename:
                    return True, str(item)

                # 检查是否是重命名后的文件（包含原始文件名）
                if original_filename in item.name and item.name.endswith(Path(original_filename).suffix):
                    return True, str(item)

        return False, None

    def get_existing_file_metadata(self, document_id: str, original_filename: str) -> Optional[Dict[str, Any]]:
        """
        获取已存在文件的元数据

        Args:
            document_id: 文档ID
            original_filename: 原始文件名

        Returns:
            文件元数据字典，如果文件不存在则返回None
        """
        exists, file_path = self.check_file_exists(document_id, original_filename)

        if not exists:
            return None

        file_path_obj = Path(file_path)

        # 尝试从文件路径推断snow_id
        snow_id = None
        if self.directory.use_snow_id_subdirs:
            # 从路径中提取snow_id：document_id/snow_id/filename
            parts = file_path_obj.parts
            if len(parts) >= 3:
                snow_id = parts[-2]  # 倒数第二个部分应该是snow_id

        if not snow_id:
            # 如果无法从路径提取，尝试从文件名提取
            stem = file_path_obj.stem
            if '_' in stem:
                potential_snow_id = stem.split('_')[0]
                if potential_snow_id.isdigit():
                    snow_id = potential_snow_id

        return {
            "raw_file_name": original_filename,
            "file_type": self._get_file_type(file_path),
            "snow_id": snow_id or "unknown",
            "tmp_file_name": file_path_obj.name,
            "timestamp": str(int(file_path_obj.stat().st_mtime)),
            "document_id": document_id,
            "file_path": file_path
        }

    def _get_file_type(self, file_path: str) -> str:
        """根据文件扩展名确定文件类型"""
        ext = Path(file_path).suffix.lower()

        if ext in self.processing.supported_pdf_extensions:
            return "pdf"
        elif ext in self.processing.supported_html_extensions:
            return "html"
        elif ext in self.processing.supported_markdown_extensions:
            return "markdown"
        else:
            return "unknown"


# 全局配置实例
config_manager = ConfigManager()
