{"concurrency": {"mineru_max_concurrent": 2, "native_max_concurrent": 4, "pandoc_max_concurrent": 4, "max_total_concurrent_tasks": 10, "mineru_max_retries": 2, "pandoc_max_retries": 1}, "directory": {"base_receive_dir": "tmp_file_receive", "base_markdown_dir": "tmp_file_markdown", "base_document_tree_dir": "tmp_document_tree", "use_snow_id_subdirs": true}, "processing": {"check_existing_files": true, "skip_existing_files": true, "supported_pdf_extensions": [".pdf"], "supported_html_extensions": [".html", ".htm"], "supported_markdown_extensions": [".md", ".markdown"]}}