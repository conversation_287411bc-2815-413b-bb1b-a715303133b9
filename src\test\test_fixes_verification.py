"""
验证修复的测试
测试Markdown文件路径问题和文档重复检查问题的修复
"""

import sys
import shutil
import time
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.config import config_manager
from src.module.FileReformat import FileReformat
from src.module.Markdown2DocumentTree import Markdown2DocumentTree


class SimpleSnowflakeIDGenerator:
    """简化的雪花ID生成器"""
    
    def __init__(self):
        self.sequence = 0
        self.last_timestamp = 0
    
    def _current_millis(self):
        return int(time.time() * 1000)
    
    def next_id(self):
        timestamp = self._current_millis()
        if timestamp == self.last_timestamp:
            self.sequence += 1
        else:
            self.sequence = 0
        self.last_timestamp = timestamp
        return timestamp * 1000 + self.sequence


def cleanup_test_files():
    """清理测试文件"""
    try:
        cleanup_dirs = [
            config_manager.directory.base_receive_dir,
            config_manager.directory.base_markdown_dir,
            config_manager.directory.base_document_tree_dir
        ]
        
        for dir_name in cleanup_dirs:
            dir_path = Path(dir_name)
            if dir_path.exists():
                shutil.rmtree(dir_path)
        
        print("测试文件清理完成")
    except Exception as e:
        print(f"清理失败: {e}")


async def test_markdown_path_fix():
    """测试Markdown文件路径修复"""
    print("=== 测试Markdown文件路径修复 ===")
    
    cleanup_test_files()
    
    try:
        snowflake = SimpleSnowflakeIDGenerator()
        document_id = "test_markdown_path"
        snow_id = str(snowflake.next_id())
        
        # 创建测试元数据
        metadata = {
            "document_id": document_id,
            "snow_id": snow_id,
            "raw_file_name": "test.md",
            "tmp_file_name": "test.md",
            "file_type": "markdown"
        }
        
        # 创建测试markdown内容
        test_content = """# 测试文档

这是一个测试文档。

## 第一章

这是第一章的内容。

### 1.1 小节

这是小节的内容。

## 第二章

这是第二章的内容。
"""
        
        # 使用配置管理器创建正确的文件路径
        receive_path = config_manager.get_file_path(
            document_id, snow_id, "test.md", "receive"
        )
        receive_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 写入源文件
        with open(receive_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"✓ 创建测试文件: {receive_path}")
        
        # 测试FileReformat的convert_to_markdown方法
        file_reformatter = FileReformat()
        markdown_path = await file_reformatter.convert_to_markdown(metadata)
        
        print(f"✓ Markdown转换成功: {markdown_path}")
        
        # 验证markdown文件存在
        markdown_file = Path(markdown_path)
        assert markdown_file.exists(), f"Markdown文件不存在: {markdown_file}"
        
        # 测试get_markdown_content方法
        content = await file_reformatter.get_markdown_content(metadata)
        assert len(content) > 0, "Markdown内容为空"
        print(f"✓ 获取Markdown内容成功，长度: {len(content)} 字符")
        
        # 测试Markdown2DocumentTree
        markdown_processor = Markdown2DocumentTree()
        tree_path = await markdown_processor.process_markdown_to_tree(metadata)
        
        print(f"✓ 文档树生成成功: {tree_path}")
        
        # 验证文档树目录存在
        tree_dir = Path(tree_path)
        assert tree_dir.exists(), f"文档树目录不存在: {tree_dir}"
        
        # 检查文档树结构
        tree_files = list(tree_dir.rglob("*.txt"))
        print(f"✓ 文档树包含 {len(tree_files)} 个文档块")
        
        for tree_file in tree_files[:3]:  # 显示前3个文件
            print(f"  - {tree_file.relative_to(tree_dir)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Markdown路径修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        cleanup_test_files()


async def test_duplicate_check_fix():
    """测试文档重复检查修复"""
    print("\n=== 测试文档重复检查修复 ===")
    
    cleanup_test_files()
    
    try:
        snowflake = SimpleSnowflakeIDGenerator()
        document_id = "test_duplicate_check"
        
        # 测试文件列表
        test_files = ["document1.pdf", "document2.pdf", "document3.pdf"]
        
        # 第一轮：上传所有文件
        print("第一轮：上传所有文件")
        uploaded_files = []
        
        for filename in test_files:
            snow_id = str(snowflake.next_id())
            
            # 创建文件路径
            file_path = config_manager.get_file_path(
                document_id, snow_id, filename, "receive"
            )
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入测试内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"测试内容 - {filename}")
            
            uploaded_files.append(filename)
            print(f"  ✓ 上传文件: {filename}")
        
        # 第二轮：检查重复文件
        print("\n第二轮：检查重复文件")
        
        for filename in test_files:
            exists, existing_path = config_manager.check_file_exists(document_id, filename)
            
            if exists:
                print(f"  ✓ 检测到重复文件: {filename} -> {existing_path}")
            else:
                print(f"  ✗ 未检测到文件: {filename}")
                return False
        
        # 第三轮：测试新文件
        print("\n第三轮：测试新文件")
        
        new_files = ["new_document1.pdf", "new_document2.pdf"]
        
        for filename in new_files:
            exists, existing_path = config_manager.check_file_exists(document_id, filename)
            
            if not exists:
                print(f"  ✓ 新文件检测正确: {filename} (不存在)")
            else:
                print(f"  ✗ 新文件检测错误: {filename} (误报存在)")
                return False
        
        # 第四轮：测试跨知识库检查
        print("\n第四轮：测试跨知识库检查")
        
        other_document_id = "other_knowledge_base"
        
        for filename in test_files:
            exists, existing_path = config_manager.check_file_exists(other_document_id, filename)
            
            if not exists:
                print(f"  ✓ 跨知识库检测正确: {filename} (在其他知识库中不存在)")
            else:
                print(f"  ✗ 跨知识库检测错误: {filename} (误报存在)")
                return False
        
        print("\n✓ 文档重复检查修复测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 文档重复检查修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        cleanup_test_files()


async def test_end_to_end_workflow():
    """测试端到端工作流程"""
    print("\n=== 测试端到端工作流程 ===")
    
    cleanup_test_files()
    
    try:
        snowflake = SimpleSnowflakeIDGenerator()
        document_id = "end_to_end_test"
        snow_id = str(snowflake.next_id())
        
        # 创建测试PDF元数据（模拟PDF转换后的结果）
        metadata = {
            "document_id": document_id,
            "snow_id": snow_id,
            "raw_file_name": "test.pdf",
            "tmp_file_name": "test.pdf",
            "file_type": "pdf"
        }
        
        # 模拟PDF文件
        receive_path = config_manager.get_file_path(
            document_id, snow_id, "test.pdf", "receive"
        )
        receive_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(receive_path, 'w', encoding='utf-8') as f:
            f.write("模拟PDF内容")
        
        print(f"✓ 创建模拟PDF文件: {receive_path}")
        
        # 模拟转换后的Markdown内容
        markdown_content = """# 测试PDF文档

这是从PDF转换而来的Markdown文档。

## 第一部分

这是第一部分的内容。

### 1.1 子章节

这是子章节的内容。

## 第二部分

这是第二部分的内容。

### 2.1 另一个子章节

这是另一个子章节的内容。
"""
        
        # 创建Markdown文件
        markdown_path = config_manager.get_file_path(
            document_id, snow_id, f"{snow_id}_markdown.md", "markdown"
        )
        markdown_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(markdown_path, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        print(f"✓ 创建Markdown文件: {markdown_path}")
        
        # 测试FileReformat的get_markdown_content方法
        file_reformatter = FileReformat()
        content = await file_reformatter.get_markdown_content(metadata)
        assert len(content) > 0, "Markdown内容为空"
        print(f"✓ 读取Markdown内容成功，长度: {len(content)} 字符")
        
        # 测试Markdown2DocumentTree
        markdown_processor = Markdown2DocumentTree()
        tree_path = await markdown_processor.process_markdown_to_tree(metadata)
        
        print(f"✓ 文档树生成成功: {tree_path}")
        
        # 验证文档树
        tree_dir = Path(tree_path)
        assert tree_dir.exists(), f"文档树目录不存在: {tree_dir}"
        
        tree_files = list(tree_dir.rglob("*.txt"))
        print(f"✓ 文档树包含 {len(tree_files)} 个文档块")
        
        # 测试文档树信息获取
        tree_info = await markdown_processor.get_document_tree_info(metadata)
        assert "error" not in tree_info, f"文档树信息获取失败: {tree_info}"
        print(f"✓ 文档树信息获取成功")
        
        # 测试文档块读取
        if tree_files:
            first_file = tree_files[0]
            chunk_content = await markdown_processor.read_document_chunk(str(first_file))
            assert len(chunk_content) > 0, "文档块内容为空"
            print(f"✓ 文档块读取成功，内容长度: {len(chunk_content)} 字符")
        
        print("\n✓ 端到端工作流程测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 端到端工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        cleanup_test_files()


async def main():
    """运行所有修复验证测试"""
    print("🔧 开始验证修复...")
    print("=" * 50)
    
    test_results = []
    
    try:
        # 运行各项测试
        test_results.append(("Markdown路径修复", await test_markdown_path_fix()))
        test_results.append(("文档重复检查修复", await test_duplicate_check_fix()))
        test_results.append(("端到端工作流程", await test_end_to_end_workflow()))
        
        print("\n" + "=" * 50)
        print("📊 修复验证结果")
        print("=" * 50)
        
        passed = 0
        total = len(test_results)
        
        for test_name, result in test_results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name:20}: {status}")
            if result:
                passed += 1
        
        print(f"\n📈 总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            print("\n🎉 所有修复验证都通过了！")
            print("\n✨ 修复验证成功：")
            print("  ✅ Markdown文件路径问题已修复")
            print("  ✅ 文档重复检查逻辑已修复")
            print("  ✅ 端到端工作流程正常")
            print("\n🚀 系统修复完成，可以正常使用！")
        else:
            print(f"\n⚠️  {total - passed} 个测试失败，需要进一步检查")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
