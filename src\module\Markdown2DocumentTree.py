"""
Markdown转文档树模块
负责将Markdown文档按照标题层级关系拆分为目录树状结构的文档块
"""

import re
import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
from src.utils.DataProcess import DataProcess

# 配置日志（同时输出到控制台和文件）
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

logger = logging.getLogger(__name__)
if not logger.handlers:  # 避免重复添加处理器
    logger.setLevel(logging.INFO)

    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 文件处理器
    file_handler = logging.handlers.TimedRotatingFileHandler(
        log_dir / "document_tree.log",
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    logger.propagate = False


@dataclass
class DocumentNode:
    """文档节点"""
    title: str
    level: int
    content: str
    children: List['DocumentNode']
    parent: Optional['DocumentNode'] = None
    
    def __post_init__(self):
        if self.children is None:
            self.children = []


class Markdown2DocumentTree:
    """Markdown转文档树处理器"""

    def __init__(self):
        from src.utils.config import config_manager
        self.data_processor = DataProcess()
        self.config = config_manager
        self.base_markdown_dir = Path(self.config.directory.base_markdown_dir)
        self.base_output_dir = Path(self.config.directory.base_document_tree_dir)

        # 确保输出目录存在
        self.base_output_dir.mkdir(exist_ok=True)
    
    async def process_markdown_to_tree(self, metadata: Dict[str, Any]) -> str:
        """
        主要功能：将Markdown转换为文档块树目录

        Args:
            metadata: 文件元数据字典

        Returns:
            生成的文档树根目录路径
        """
        try:
            logger.info(f"[TREE_START] 开始处理Markdown转文档树 - document_id: {metadata['document_id']}")

            # 读取markdown文件 - 使用配置管理器获取正确路径
            markdown_file = self.config.get_file_path(
                metadata["document_id"],
                metadata["snow_id"],
                f"{metadata['snow_id']}_markdown.md",
                "markdown"
            )

            if not markdown_file.exists():
                logger.error(f"[TREE_ERROR] Markdown文件不存在: {markdown_file}")
                raise FileNotFoundError(f"Markdown文件不存在: {markdown_file}")

            logger.info(f"[TREE_READ] 读取Markdown文件: {markdown_file}")
            content = await self.data_processor.read_file(str(markdown_file))
            markdown_text = content.decode('utf-8', errors='ignore')

            logger.info(f"[TREE_PARSE] 开始解析Markdown为文档树 - 文件大小: {len(markdown_text)} 字符")

            # 解析markdown为文档树
            root_node = self._parse_markdown_to_tree(markdown_text)

            # 统计节点信息
            total_nodes = self._count_nodes(root_node)
            logger.info(f"[TREE_PARSED] 文档树解析完成 - 总节点数: {total_nodes}")

            # 创建输出目录 - 使用配置管理器获取正确路径
            output_dir = self.config.get_file_path(
                metadata["document_id"],
                metadata["snow_id"],
                "document_tree",
                "tree"
            ).parent  # 获取父目录作为输出目录
            output_dir.mkdir(parents=True, exist_ok=True)

            logger.info(f"[TREE_GENERATE] 开始生成文档树目录结构: {output_dir}")

            # 生成文档树目录结构
            await self._generate_document_tree(root_node, output_dir, metadata)

            logger.info(f"[TREE_SUCCESS] 文档树生成完成 - 输出目录: {output_dir}")
            return str(output_dir)

        except Exception as e:
            logger.error(f"[TREE_ERROR] Markdown转文档树失败: {e}")
            raise Exception(f"Markdown转文档树失败: {e}")
    
    def _parse_markdown_to_tree(self, markdown_text: str) -> DocumentNode:
        """解析Markdown文本为文档树"""
        lines = markdown_text.split('\n')
        
        # 创建根节点
        root = DocumentNode(title="根文档", level=0, content="", children=[])
        current_nodes = [root]  # 当前层级的节点栈
        current_content = []
        
        for line in lines:
            # 检查是否是标题行
            heading_match = re.match(r'^(#{1,6})\s+(.+)$', line.strip())
            
            if heading_match:
                # 保存之前的内容
                if current_content:
                    content_text = '\n'.join(current_content).strip()
                    if content_text and current_nodes:
                        current_nodes[-1].content = content_text
                    current_content = []
                
                # 解析标题
                level = len(heading_match.group(1))
                title = heading_match.group(2).strip()
                
                # 创建新节点
                new_node = DocumentNode(title=title, level=level, content="", children=[])
                
                # 找到合适的父节点
                while len(current_nodes) > 1 and current_nodes[-1].level >= level:
                    current_nodes.pop()
                
                # 添加到父节点
                parent = current_nodes[-1]
                new_node.parent = parent
                parent.children.append(new_node)
                current_nodes.append(new_node)
                
            else:
                # 普通内容行
                if line.strip():  # 忽略空行
                    current_content.append(line)
        
        # 处理最后的内容
        if current_content and current_nodes:
            content_text = '\n'.join(current_content).strip()
            if content_text:
                current_nodes[-1].content = content_text
        
        return root

    def _count_nodes(self, node: DocumentNode) -> int:
        """递归计算节点总数"""
        count = 1  # 当前节点
        for child in node.children:
            count += self._count_nodes(child)
        return count
    
    async def _generate_document_tree(self, node: DocumentNode, base_dir: Path, metadata: Dict[str, Any], sequence_counter: List[int] = None) -> None:
        """生成文档树目录结构"""
        if sequence_counter is None:
            sequence_counter = [0]

        logger.info(f"[TREE_GENERATE_START] 开始生成文档树 - 根节点子节点数: {len(node.children)}")

        # 为根节点的子节点创建目录
        for i, child in enumerate(node.children):
            logger.info(f"[TREE_NODE] 处理节点 {i+1}/{len(node.children)}: {child.title} (级别: {child.level})")
            await self._create_node_directory(child, base_dir, metadata, sequence_counter)

        logger.info(f"[TREE_GENERATE_COMPLETE] 文档树生成完成 - 总文件数: {sequence_counter[0]}")
    
    async def _create_node_directory(self, node: DocumentNode, parent_dir: Path, metadata: Dict[str, Any], sequence_counter: List[int]) -> None:
        """为节点创建目录和文件"""
        # 清理标题作为目录名（移除特殊字符）
        safe_title = self._sanitize_filename(node.title)
        node_dir = parent_dir / safe_title
        node_dir.mkdir(exist_ok=True)
        
        # 如果节点有内容，创建文本文件
        if node.content.strip():
            sequence_counter[0] += 1
            content_file = node_dir / f"{metadata['snow_id']}_{sequence_counter[0]}.txt"
            await self.data_processor.write_file_normal(
                str(content_file), 
                node.content.encode('utf-8')
            )
        
        # 递归处理子节点
        for child in node.children:
            await self._create_node_directory(child, node_dir, metadata, sequence_counter)
    
    def _sanitize_filename(self, filename: str) -> str:
        """清理文件名，移除不安全的字符"""
        # 移除或替换不安全的字符
        unsafe_chars = r'[<>:"/\\|?*]'
        safe_name = re.sub(unsafe_chars, '_', filename)
        
        # 移除前后空格和点
        safe_name = safe_name.strip('. ')
        
        # 如果名称为空或太长，使用默认名称
        if not safe_name or len(safe_name) > 100:
            safe_name = "unnamed_section"
        
        return safe_name
    
    def _extract_headings(self, markdown_text: str) -> List[Tuple[int, str, int]]:
        """提取所有标题及其位置信息"""
        headings = []
        lines = markdown_text.split('\n')
        
        for i, line in enumerate(lines):
            heading_match = re.match(r'^(#{1,6})\s+(.+)$', line.strip())
            if heading_match:
                level = len(heading_match.group(1))
                title = heading_match.group(2).strip()
                headings.append((level, title, i))
        
        return headings
    
    def _split_content_by_headings(self, markdown_text: str) -> List[Dict[str, Any]]:
        """按标题分割内容"""
        lines = markdown_text.split('\n')
        headings = self._extract_headings(markdown_text)
        sections = []
        
        for i, (level, title, line_num) in enumerate(headings):
            # 确定内容范围
            start_line = line_num + 1
            end_line = headings[i + 1][2] if i + 1 < len(headings) else len(lines)
            
            # 提取内容
            content_lines = lines[start_line:end_line]
            content = '\n'.join(content_lines).strip()
            
            sections.append({
                'level': level,
                'title': title,
                'content': content,
                'line_start': start_line,
                'line_end': end_line
            })
        
        return sections
    
    async def get_document_tree_info(self, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """获取文档树信息"""
        # 使用配置管理器获取正确的输出目录路径
        output_dir = self.config.get_file_path(
            metadata["document_id"],
            metadata["snow_id"],
            "document_tree",
            "tree"
        ).parent

        if not output_dir.exists():
            return {"error": "文档树不存在"}

        tree_info = {
            "document_id": metadata["document_id"],
            "snow_id": metadata["snow_id"],
            "tree_path": str(output_dir),
            "structure": await self._get_directory_structure(output_dir)
        }

        return tree_info
    
    async def _get_directory_structure(self, directory: Path) -> Dict[str, Any]:
        """获取目录结构信息"""
        structure = {
            "name": directory.name,
            "type": "directory",
            "children": []
        }
        
        try:
            if directory.exists() and directory.is_dir():
                for item in sorted(directory.iterdir()):
                    if item.is_dir():
                        child_structure = await self._get_directory_structure(item)
                        structure["children"].append(child_structure)
                    elif item.is_file() and item.suffix == '.txt':
                        structure["children"].append({
                            "name": item.name,
                            "type": "file",
                            "path": str(item),
                            "size": item.stat().st_size
                        })
        except (PermissionError, OSError) as e:
            structure["error"] = f"访问错误: {e}"
        
        return structure
    
    async def read_document_chunk(self, file_path: str) -> str:
        """读取文档块内容"""
        try:
            content = await self.data_processor.read_file(file_path)
            return content.decode('utf-8', errors='ignore')
        except Exception as e:
            raise Exception(f"读取文档块失败: {e}")
    
    def get_relative_levels(self, headings: List[Tuple[int, str, int]]) -> List[Tuple[int, str, int, int]]:
        """
        获取相对标题层级
        因为markdown中的标题层级是相对的概念，需要根据实际的标题分级进行处理
        """
        if not headings:
            return []
        
        # 找到最小的标题级别作为根级别
        min_level = min(heading[0] for heading in headings)
        
        # 转换为相对层级
        relative_headings = []
        for level, title, line_num in headings:
            relative_level = level - min_level + 1
            relative_headings.append((level, title, line_num, relative_level))
        
        return relative_headings
