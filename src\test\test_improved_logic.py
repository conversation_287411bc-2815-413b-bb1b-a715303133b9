"""
测试改进后的逻辑
测试document_id层级结构、文档存在性检查、并发控制等功能
"""

import asyncio
import pytest
import shutil
import time
import sys
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from src.utils.config import config_manager
from src.utils.DataProcess import DataProcess
from src.module.FileReformat import FileReformat
from src.service.logllm_data_access_service import LogLLMDataAccessService


class TestImprovedLogic:
    """测试改进后的逻辑"""
    
    def setup_method(self):
        """测试前的设置"""
        self.data_processor = DataProcess()
        self.file_reformatter = FileReformat()
        self.service = LogLLMDataAccessService()
        
        # 测试用的document_id
        self.test_document_id = "test_knowledge_base_001"
        
        # 测试文件路径
        self.test_files = [
            "src/test/A.pdf",
            "src/test/B.pdf", 
            "src/test/C.pdf"
        ]
        
        # 清理测试环境
        self.cleanup_test_files()
    
    def teardown_method(self):
        """测试后的清理"""
        self.cleanup_test_files()
    
    def cleanup_test_files(self):
        """清理测试文件"""
        try:
            cleanup_dirs = [
                config_manager.directory.base_receive_dir,
                config_manager.directory.base_markdown_dir,
                config_manager.directory.base_document_tree_dir
            ]
            
            for dir_name in cleanup_dirs:
                dir_path = Path(dir_name)
                if dir_path.exists():
                    shutil.rmtree(dir_path)
            
            print("测试文件清理完成")
        except Exception as e:
            print(f"清理失败: {e}")
    
    def test_directory_structure(self):
        """测试目录结构：document_id/snow_id/文档"""
        print("\n=== 测试目录结构 ===")
        
        # 确保配置使用snow_id子目录
        assert config_manager.directory.use_snow_id_subdirs == True
        
        # 测试路径生成
        test_path = config_manager.get_file_path(
            "test_doc", "123456789", "test.pdf", "receive"
        )
        
        expected_path = Path(config_manager.directory.base_receive_dir) / "test_doc" / "123456789" / "test.pdf"
        assert test_path == expected_path
        
        print(f"✓ 目录结构正确: {test_path}")
    
    async def test_file_existence_check(self):
        """测试文件存在性检查逻辑"""
        print("\n=== 测试文件存在性检查 ===")
        
        # 首先上传一个文件
        test_file = Path(self.test_files[0])
        if not test_file.exists():
            print(f"测试文件不存在: {test_file}")
            return
        
        # 读取文件内容
        with open(test_file, 'rb') as f:
            file_content = f.read()
        
        # 第一次上传
        metadata1 = await self.data_processor.write_file_tmp(
            file_content, test_file.name, self.test_document_id
        )
        print(f"✓ 第一次上传成功: {metadata1['snow_id']}")
        
        # 检查文件是否存在
        exists, existing_path = config_manager.check_file_exists(
            self.test_document_id, test_file.name
        )
        assert exists == True
        print(f"✓ 文件存在性检查正确: {existing_path}")
        
        # 第二次上传同一个文件（应该跳过）
        metadata2 = await self.data_processor.write_file_tmp(
            file_content, test_file.name, self.test_document_id
        )
        
        # 应该返回已存在的文件信息
        assert metadata2['snow_id'] == metadata1['snow_id']
        print(f"✓ 重复文件跳过逻辑正确")
    
    async def test_concurrent_processing(self):
        """测试并发处理"""
        print("\n=== 测试并发处理 ===")
        
        # 检查并发配置
        print(f"MinerU并发数: {config_manager.concurrency.mineru_max_concurrent}")
        print(f"Native并发数: {config_manager.concurrency.native_max_concurrent}")
        print(f"Pandoc并发数: {config_manager.concurrency.pandoc_max_concurrent}")
        print(f"总并发数: {config_manager.concurrency.max_total_concurrent_tasks}")
        
        # 测试信号量是否正确设置
        assert self.file_reformatter.mineru_semaphore._value == config_manager.concurrency.mineru_max_concurrent
        assert self.file_reformatter.native_semaphore._value == config_manager.concurrency.native_max_concurrent
        assert self.file_reformatter.pandoc_semaphore._value == config_manager.concurrency.pandoc_max_concurrent
        
        print("✓ 并发配置正确")
    
    async def test_multiple_files_processing(self):
        """测试多文件处理"""
        print("\n=== 测试多文件处理 ===")
        
        tasks = []
        for i, test_file_path in enumerate(self.test_files):
            test_file = Path(test_file_path)
            if not test_file.exists():
                print(f"跳过不存在的测试文件: {test_file}")
                continue
            
            # 为每个文件使用不同的document_id
            document_id = f"{self.test_document_id}_{i}"
            
            # 创建处理任务
            task = self.process_single_file(test_file, document_id)
            tasks.append(task)
        
        if tasks:
            # 并发处理所有文件
            start_time = time.time()
            results = await asyncio.gather(*tasks, return_exceptions=True)
            duration = time.time() - start_time
            
            print(f"✓ 处理了 {len(tasks)} 个文件，耗时: {duration:.2f}秒")
            
            # 检查结果
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    print(f"✗ 文件 {i} 处理失败: {result}")
                else:
                    print(f"✓ 文件 {i} 处理成功: {result}")
        else:
            print("没有找到测试文件")
    
    async def process_single_file(self, test_file: Path, document_id: str) -> str:
        """处理单个文件"""
        try:
            # 读取文件内容
            with open(test_file, 'rb') as f:
                file_content = f.read()
            
            # 上传文件
            metadata = await self.data_processor.write_file_tmp(
                file_content, test_file.name, document_id
            )
            
            # 转换为markdown
            markdown_path = await self.file_reformatter.convert_to_markdown(metadata)
            
            return f"成功处理: {test_file.name} -> {markdown_path}"
            
        except Exception as e:
            return f"处理失败: {test_file.name} - {e}"
    
    async def test_configuration_loading(self):
        """测试配置加载"""
        print("\n=== 测试配置加载 ===")
        
        # 检查配置是否正确加载
        assert config_manager.concurrency.mineru_max_concurrent > 0
        assert config_manager.concurrency.native_max_concurrent > 0
        assert config_manager.concurrency.pandoc_max_concurrent > 0
        
        print(f"✓ 配置加载正确")
        print(f"  - MinerU并发: {config_manager.concurrency.mineru_max_concurrent}")
        print(f"  - Native并发: {config_manager.concurrency.native_max_concurrent}")
        print(f"  - Pandoc并发: {config_manager.concurrency.pandoc_max_concurrent}")
        print(f"  - 使用snow_id子目录: {config_manager.directory.use_snow_id_subdirs}")
        print(f"  - 检查已存在文件: {config_manager.processing.check_existing_files}")
    
    def test_path_generation(self):
        """测试路径生成"""
        print("\n=== 测试路径生成 ===")
        
        document_id = "test_doc"
        snow_id = "123456789"
        filename = "test.pdf"
        
        # 测试不同类型的路径生成
        receive_path = config_manager.get_file_path(document_id, snow_id, filename, "receive")
        markdown_path = config_manager.get_file_path(document_id, snow_id, filename, "markdown")
        tree_path = config_manager.get_file_path(document_id, snow_id, filename, "tree")
        
        print(f"✓ 接收路径: {receive_path}")
        print(f"✓ Markdown路径: {markdown_path}")
        print(f"✓ 文档树路径: {tree_path}")
        
        # 验证路径结构
        assert str(receive_path).endswith(f"{document_id}/{snow_id}/{filename}")
        assert str(markdown_path).endswith(f"{document_id}/{snow_id}/{filename}")
        assert str(tree_path).endswith(f"{document_id}/{snow_id}/{filename}")


async def run_tests():
    """运行所有测试"""
    test_instance = TestImprovedLogic()
    
    try:
        test_instance.setup_method()
        
        print("开始测试改进后的逻辑...")
        
        # 运行同步测试
        test_instance.test_directory_structure()
        test_instance.test_path_generation()
        
        # 运行异步测试
        await test_instance.test_configuration_loading()
        await test_instance.test_file_existence_check()
        await test_instance.test_concurrent_processing()
        await test_instance.test_multiple_files_processing()
        
        print("\n=== 所有测试完成 ===")
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    finally:
        test_instance.teardown_method()


if __name__ == "__main__":
    asyncio.run(run_tests())
